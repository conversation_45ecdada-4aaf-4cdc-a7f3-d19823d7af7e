using Witlab.Platform.UseCases.Platform.Sessions;
using Witlab.Platform.Web.Configurations.Auth;

namespace Witlab.Platform.Web.Endpoints.Platform.Sessions;

/// <summary>
/// 获取活跃会话请求
/// </summary>
public class GetActiveSessionsRequest
{
  /// <summary>
  /// 页码
  /// </summary>
  public int PageNumber { get; set; } = 1;

  /// <summary>
  /// 页大小
  /// </summary>
  public int PageSize { get; set; } = 20;
}

/// <summary>
/// 获取活跃会话响应
/// </summary>
public class GetActiveSessionsResponse
{
  /// <summary>
  /// 会话列表
  /// </summary>
  public List<UserSessionDTO> Sessions { get; set; } = new();

  /// <summary>
  /// 总数量
  /// </summary>
  public int TotalCount { get; set; }

  /// <summary>
  /// 页码
  /// </summary>
  public int PageNumber { get; set; }

  /// <summary>
  /// 页大小
  /// </summary>
  public int PageSize { get; set; }

  /// <summary>
  /// 总页数
  /// </summary>
  public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
}

/// <summary>
/// 获取活跃会话接口
/// </summary>
public class GetActiveSessions(IMediator mediator) : Endpoint<GetActiveSessionsRequest, GetActiveSessionsResponse>
{
  public override void Configure()
  {
    Get("/platform/sessions/active");
    Description(x => x.WithTags("Sessions"));
    Policies(RequirePermissionAttribute.POLICY_PREFIX + "session:view");
    Summary(s =>
    {
      s.Summary = "获取活跃会话列表";
      s.Description = "获取当前所有活跃的用户会话信息，支持分页";
      s.ExampleRequest = new GetActiveSessionsRequest { PageNumber = 1, PageSize = 20 };
    });
  }

  public override async Task HandleAsync(GetActiveSessionsRequest request, CancellationToken cancellationToken)
  {
    var query = new GetActiveSessionsQuery(request.PageNumber, request.PageSize);
    var result = await mediator.Send(query, cancellationToken);

    if (result.IsSuccess)
    {
      var (sessions, totalCount) = result.Value;
      Response = new GetActiveSessionsResponse
      {
        Sessions = sessions,
        TotalCount = totalCount,
        PageNumber = request.PageNumber,
        PageSize = request.PageSize
      };
      return;
    }

    foreach (var error in result.Errors)
    {
      AddError(error);
    }

    await SendErrorsAsync();
  }
}
