﻿<Project Sdk="Microsoft.NET.Sdk">
  <Sdk Name="Microsoft.Build.CentralPackageVersions" Version="2.1.3" />

  <PropertyGroup>
    <PreserveCompilationContext>true</PreserveCompilationContext>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Core\Services\ToDoItemSearchService_GetAllIncompleteItems.cs" />
    <Compile Remove="Core\Services\ToDoItemSearchService_GetNextIncompleteItem.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Shouldly" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="Moq" />
    <PackageReference Include="NSubstitute" />
    <PackageReference Include="xunit.runner.visualstudio">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="ReportGenerator" />
    <PackageReference Include="xunit" />
  </ItemGroup>

  <ItemGroup>
    <None Update="xunit.runner.json">
	  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Core\Services\" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\Witlab.Platform.Core\Witlab.Platform.Core.csproj" />
    <ProjectReference Include="..\..\src\Witlab.Platform.UseCases\Witlab.Platform.UseCases.csproj" />
  </ItemGroup>

</Project>
