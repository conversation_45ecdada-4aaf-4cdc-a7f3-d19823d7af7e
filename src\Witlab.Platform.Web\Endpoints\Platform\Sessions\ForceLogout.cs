using Witlab.Platform.UseCases.Platform.Sessions;
using Witlab.Platform.Web.Configurations.Auth;

namespace Witlab.Platform.Web.Endpoints.Platform.Sessions;

/// <summary>
/// 强制下线请求
/// </summary>
public class ForceLogoutRequest
{
  /// <summary>
  /// 会话ID
  /// </summary>
  public Guid SessionId { get; set; }

  /// <summary>
  /// 下线原因
  /// </summary>
  public string? Reason { get; set; }
}

/// <summary>
/// 强制下线接口
/// </summary>
public class ForceLogout(IMediator mediator) : Endpoint<ForceLogoutRequest>
{
  public override void Configure()
  {
    Post("/platform/sessions/{sessionId}/force-logout");
    Description(x => x.WithTags("Sessions"));
    Policies(RequirePermissionAttribute.POLICY_PREFIX + "session:manage");
    Summary(s =>
    {
      s.Summary = "强制下线指定会话";
      s.Description = "管理员强制下线指定的用户会话";
      s.ExampleRequest = new ForceLogoutRequest
      {
        SessionId = Guid.NewGuid(),
        Reason = "违规操作"
      };
    });
  }

  public override async Task HandleAsync(ForceLogoutRequest request, CancellationToken cancellationToken)
  {
    var command = new ForceLogoutCommand(request.SessionId, request.Reason);
    var result = await mediator.Send(command, cancellationToken);

    if (result.IsSuccess)
    {
      await SendNoContentAsync(cancellationToken);
      return;
    }

    foreach (var error in result.Errors)
    {
      AddError(error);
    }

    await SendErrorsAsync();
  }
}

/// <summary>
/// 强制下线用户所有会话请求
/// </summary>
public class ForceLogoutAllUserSessionsRequest
{
  /// <summary>
  /// 用户ID
  /// </summary>
  public Guid UserId { get; set; }

  /// <summary>
  /// 下线原因
  /// </summary>
  public string? Reason { get; set; }
}

/// <summary>
/// 强制下线用户所有会话接口
/// </summary>
public class ForceLogoutAllUserSessions(IMediator mediator) : Endpoint<ForceLogoutAllUserSessionsRequest>
{
  public override void Configure()
  {
    Post("/platform/sessions/user/{userId}/force-logout-all");
    Description(x => x.WithTags("Sessions"));
    Policies(RequirePermissionAttribute.POLICY_PREFIX + "session:manage");
    Summary(s =>
    {
      s.Summary = "强制下线用户所有会话";
      s.Description = "管理员强制下线指定用户的所有活跃会话";
      s.ExampleRequest = new ForceLogoutAllUserSessionsRequest
      {
        UserId = Guid.NewGuid(),
        Reason = "账户安全检查"
      };
    });
  }

  public override async Task HandleAsync(ForceLogoutAllUserSessionsRequest request, CancellationToken cancellationToken)
  {
    var command = new ForceLogoutAllUserSessionsCommand(request.UserId, request.Reason);
    var result = await mediator.Send(command, cancellationToken);

    if (result.IsSuccess)
    {
      await SendNoContentAsync(cancellationToken);
      return;
    }

    foreach (var error in result.Errors)
    {
      AddError(error);
    }

    await SendErrorsAsync();
  }
}
