﻿using Witlab.Platform.Core.Platform.Events;
using Witlab.Platform.Core.Platform.ValueObjects;

namespace Witlab.Platform.Core.Platform;

/// <summary>
/// 用户会话聚合根
/// </summary>
public class UserSession : AuditableEntityBase<Guid>, IAggregateRoot
{
  /// <summary>
  /// 用户ID
  /// </summary>
  public Guid UserId { get; private set; }

  /// <summary>
  /// 用户名
  /// </summary>
  public string UserName { get; private set; } = string.Empty;

  /// <summary>
  /// JWT Token ID (JTI)
  /// </summary>
  public string TokenId { get; private set; } = string.Empty;

  /// <summary>
  /// 登录时间
  /// </summary>
  public DateTime LoginTime { get; private set; }

  /// <summary>
  /// 最后活动时间
  /// </summary>
  public DateTime LastActivityTime { get; private set; }

  /// <summary>
  /// Token过期时间
  /// </summary>
  public DateTime TokenExpiresAt { get; private set; }

  /// <summary>
  /// 会话信息
  /// </summary>
  public SessionInfo SessionInfo { get; private set; } = new SessionInfo();

  /// <summary>
  /// 会话状态
  /// </summary>
  public SessionStatus Status { get; private set; } = SessionStatus.Active;

  /// <summary>
  /// 登录来源
  /// </summary>
  public LoginSource LoginSource { get; private set; } = LoginSource.Web;

  /// <summary>
  /// 是否为当前活跃会话
  /// </summary>
  public bool IsActive => Status == SessionStatus.Active && DateTime.UtcNow < TokenExpiresAt;

  /// <summary>
  /// 是否已过期
  /// </summary>
  public bool IsExpired => DateTime.UtcNow >= TokenExpiresAt;

  private UserSession()
  {
    // for efcore
  }

  public UserSession(
    Guid userId,
    string userName,
    string tokenId,
    DateTime tokenExpiresAt,
    SessionInfo sessionInfo,
    LoginSource loginSource = LoginSource.Web)
  {
    UserId = Guard.Against.Default(userId, nameof(userId));
    UserName = Guard.Against.NullOrEmpty(userName, nameof(userName));
    TokenId = Guard.Against.NullOrEmpty(tokenId, nameof(tokenId));
    TokenExpiresAt = Guard.Against.Default(tokenExpiresAt, nameof(tokenExpiresAt));
    SessionInfo = Guard.Against.Null(sessionInfo, nameof(sessionInfo));
    LoginSource = loginSource;

    LoginTime = DateTime.UtcNow;
    LastActivityTime = DateTime.UtcNow;
    Status = SessionStatus.Active;

    this.RegisterDomainEvent(new UserSessionCreatedEvent(Id, UserId, UserName, SessionInfo.IpAddress));
  }

  /// <summary>
  /// 从缓存数据重建会话对象（用于反序列化）
  /// </summary>
  public static UserSession FromCache(
    Guid id,
    Guid userId,
    string userName,
    string tokenId,
    DateTime loginTime,
    DateTime lastActivityTime,
    DateTime tokenExpiresAt,
    SessionInfo sessionInfo,
    SessionStatus status,
    LoginSource loginSource)
  {
    var session = new UserSession(userId, userName, tokenId, tokenExpiresAt, sessionInfo, loginSource);

    // 设置从缓存恢复的状态
    session.LoginTime = loginTime;
    session.LastActivityTime = lastActivityTime;
    session.Status = status;

    // 使用反射设置ID（这是必要的，因为ID是只读的）
    var idProperty = typeof(UserSession).BaseType?.GetProperty("Id");
    if (idProperty != null && idProperty.CanWrite)
    {
      idProperty.SetValue(session, id);
    }
    else
    {
      // 如果属性不可写，尝试设置backing field
      var idField = typeof(UserSession).BaseType?.GetField("<Id>k__BackingField",
        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
      idField?.SetValue(session, id);
    }

    return session;
  }

  /// <summary>
  /// 更新最后活动时间
  /// </summary>
  public void UpdateActivity()
  {
    if (Status != SessionStatus.Active)
      return;

    LastActivityTime = DateTime.UtcNow;
  }

  /// <summary>
  /// 强制下线
  /// </summary>
  /// <param name="reason">下线原因</param>
  public void ForceLogout(string? reason = null)
  {
    if (Status == SessionStatus.Active)
    {
      Status = SessionStatus.ForcedLogout;
      this.RegisterDomainEvent(new UserSessionTerminatedEvent(Id, UserId, UserName, reason ?? "管理员强制下线"));
    }
  }

  /// <summary>
  /// 正常注销
  /// </summary>
  public void Logout()
  {
    if (Status == SessionStatus.Active)
    {
      Status = SessionStatus.LoggedOut;
      this.RegisterDomainEvent(new UserSessionTerminatedEvent(Id, UserId, UserName, "用户主动注销"));
    }
  }

  /// <summary>
  /// 标记为过期
  /// </summary>
  public void MarkAsExpired()
  {
    if (Status == SessionStatus.Active && IsExpired)
    {
      Status = SessionStatus.Expired;
    }
  }

  /// <summary>
  /// 更新会话信息
  /// </summary>
  /// <param name="sessionInfo">新的会话信息</param>
  public void UpdateSessionInfo(SessionInfo sessionInfo)
  {
    SessionInfo = Guard.Against.Null(sessionInfo, nameof(sessionInfo));
  }
}

/// <summary>
/// 会话状态枚举
/// </summary>
public enum SessionStatus
{
  /// <summary>
  /// 活跃
  /// </summary>
  Active = 1,

  /// <summary>
  /// 已注销
  /// </summary>
  LoggedOut = 2,

  /// <summary>
  /// 已过期
  /// </summary>
  Expired = 3,

  /// <summary>
  /// 被强制下线
  /// </summary>
  ForcedLogout = 4
}

/// <summary>
/// 登录来源枚举
/// </summary>
public enum LoginSource
{
  /// <summary>
  /// Web浏览器
  /// </summary>
  Web = 1,

  /// <summary>
  /// 移动应用
  /// </summary>
  Mobile = 2,

  /// <summary>
  /// 桌面应用
  /// </summary>
  Desktop = 3,

  /// <summary>
  /// API调用
  /// </summary>
  Api = 4
}
