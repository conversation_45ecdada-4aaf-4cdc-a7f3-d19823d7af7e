using Witlab.Platform.UseCases.Platform.Sessions;
using Witlab.Platform.Web.Configurations.Auth;

namespace Witlab.Platform.Web.Endpoints.Platform.Sessions;

/// <summary>
/// 获取会话统计信息接口
/// </summary>
public class GetSessionStats(IMediator mediator) : EndpointWithoutRequest<SessionStatisticsDTO>
{
  public override void Configure()
  {
    Get("/platform/sessions/statistics");
    Description(x => x.WithTags("Sessions"));
    Policies(RequirePermissionAttribute.POLICY_PREFIX + "session:view");
    Summary(s =>
    {
      s.Summary = "获取会话统计信息";
      s.Description = "获取当前系统的会话统计信息，包括在线用户数、活跃会话数等";
    });
  }

  public override async Task HandleAsync(CancellationToken cancellationToken)
  {
    var query = new GetSessionStatisticsQuery();
    var result = await mediator.Send(query, cancellationToken);

    if (result.IsSuccess)
    {
      Response = result.Value;
      return;
    }

    foreach (var error in result.Errors)
    {
      AddError(error);
    }

    await SendErrorsAsync();
  }
}
